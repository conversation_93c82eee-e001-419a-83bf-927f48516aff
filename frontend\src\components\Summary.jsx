import React, { useState } from "react";
import { useSummary } from '../context/SummaryContext';
import { generateQuote, downloadQuote, downloadWordQuote, editProposalField } from '../config/api';
import Alert from '@mui/material/Alert';
import AlertTitle from '@mui/material/AlertTitle';
import { styled } from '@mui/material/styles';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import CircularProgress from '@mui/material/CircularProgress';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import MenuItem from '@mui/material/MenuItem';
// Import Lucide icons for the new UI
import { ChevronDown, ChevronUp, Edit, Eye, Download, FileText, DollarSign } from 'lucide-react';

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    //backgroundColor: '#38aee4', //
    backgroundColor: '#1e3a8a', // darker blue

    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: theme.palette.action.hover,
  },
  '&:nth-of-type(even)': {
    backgroundColor: theme.palette.common.white,
  },
  '&:last-child td, &:last-child th': {
    border: 0,
  },
}));

// New UI Components based on tabletemplate.jsx
const getStatusColor = (status) => {
  switch (status) {
    case 'good': return 'bg-green-100 text-green-800';
    case 'warning': return 'bg-yellow-100 text-yellow-800';
    case 'pending': return 'bg-blue-100 text-blue-800';
    case 'profitable': return 'bg-green-100 text-green-800';
    case 'not-profitable': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const SectionHeader = ({ title, expanded, onToggle, icon }) => (
  <div
    className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-900 to-blue-800 text-white cursor-pointer hover:from-blue-900 hover:to-blue-950 transition-all duration-200 rounded-t-xl"
    onClick={onToggle}
  >
    <div className="flex items-center space-x-2">
      {icon}
      <h3 className="font-semibold text-lg">{title}</h3>
    </div>
    {expanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
  </div>
);

// Update the container div in each table component to include rounded corners
// Example for FinancialSummaryTable (apply similar changes to other table components):
const DataRow = ({ label, value, status, index }) => (
  <div className={`flex items-center justify-between py-3 px-4 border-b border-gray-100 
    ${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'} 
    hover:bg-gray-100 transition-colors duration-150`}>
    <span className="text-gray-700 font-medium">{label}</span>
    <div className="flex items-center space-x-2">
      {status ? (
        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status)}`}>
          {value}
        </span>
      ) : (
        <span className="text-gray-900 font-semibold">{value}</span>
      )}
    </div>
  </div>
);

const ActionButton = ({ icon, label, variant = 'primary', onClick, disabled = false }) => {
  const baseClasses = "flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md";
  const variants = {
    primary: "bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-400",
    secondary: "bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:bg-gray-100",
    success: "bg-green-600 text-white hover:bg-green-700 disabled:bg-green-400",
    warning: "bg-yellow-500 text-white hover:bg-yellow-600 disabled:bg-yellow-300"
  };

  return (
    <button
      className={`${baseClasses} ${variants[variant]} ${disabled ? 'cursor-not-allowed' : ''}`}
      onClick={onClick}
      disabled={disabled}
    >
      {icon}
      <span>{label}</span>
    </button>
  );
};

const FinancialSummaryTable = ({ data, expanded, onToggle }) => {
  if (!data || !data.financial_summary || data.financial_summary.length === 0) {
    return (
      <div className="mb-6 overflow-hidden rounded-xl shadow-lg border border-gray-200">
        <SectionHeader
          title="Financial Summary"
          expanded={expanded}
          onToggle={onToggle}
          icon={<DollarSign size={20} />}
        />
        {expanded && (
          <div className="bg-white rounded-b-xl p-4">
            <p className="text-gray-500 italic">No financial data available</p>
          </div>
        )}
      </div>
    );
  }

  // Function to strip brackets from numerical values
  const stripBrackets = (value) => {
    if (value === null || value === undefined || value === '') {
      return value;
    }
    // Convert to string and remove brackets
    return String(value).replace(/[()]/g, '');
  };

  // Compute status based on profit_after_tax
  const computeStatus = (profitAfterTax) => {
    const cleanValue = stripBrackets(profitAfterTax);
    const num = Number(String(cleanValue).replace(/,/g, ''));
    if (!isNaN(num)) {
      if (num > 0) return { text: 'Profitable', status: 'profitable' };
      if (num < 0) return { text: 'Not profitable', status: 'not-profitable' };
    }
    return { text: '-', status: null };
  };

  // Sort by fiscal_year descending (most recent first)
  const sortedFinancialSummary = [...data.financial_summary].sort((a, b) => {
    // Try to parse as integer, fallback to string compare
    const yearA = parseInt(a.fiscal_year, 10);
    const yearB = parseInt(b.fiscal_year, 10);
    if (!isNaN(yearA) && !isNaN(yearB)) {
      return yearB - yearA;
    }
    return String(b.fiscal_year).localeCompare(String(a.fiscal_year));
  });

  // Check if the client is profitable (any year profitable)
  const isProfitable = sortedFinancialSummary.some(item => {
    const num = Number(String(stripBrackets(item.profit_after_tax)).replace(/,/g, ''));
    return !isNaN(num) && num > 0;
  });

  // Create horizontal table structure with fiscal years as first column
  const fieldNames = ['Status', 'Revenue (KES)', 'Operating Profit (KES)', 'Profit Before Tax (KES)', 'Profit After Tax (KES)', 'Source File'];
  const tableHeaders = ['Fiscal Year', ...fieldNames];

  // Create table rows with fiscal years as the first column
  const tableRows = sortedFinancialSummary.map(item => {
    const status = computeStatus(item.profit_after_tax);
    return {
      fiscalYear: ` ${item.fiscal_year}`,
      values: [
        { value: status.text, status: status.status },
        { value: stripBrackets(item.revenue) || '-' },
        { value: stripBrackets(item.operating_profit) || '-' },
        { value: stripBrackets(item.profit_before_tax) || '-' },
        { value: stripBrackets(item.profit_after_tax) || '-' },
        { value: item.source_file || '-' }
      ]
    };
  });

  return (
    <div className="mb-6 overflow-hidden rounded-xl shadow-lg border border-gray-200">
      <SectionHeader
        title="Financial Summary"
        expanded={expanded}
        onToggle={onToggle}
        icon={<DollarSign size={20} />}
      />
      {expanded && (
        <div className="bg-white rounded-b-xl">
          {/* Horizontal Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-50">
                  {tableHeaders.map((header, index) => (
                    <th key={index} className="px-4 py-3 text-left text-sm font-semibold text-gray-700 border-b border-gray-200">
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {tableRows.map((row, rowIndex) => (
                  <tr key={rowIndex} className={`hover:bg-gray-100 transition-colors duration-150 ${
                    rowIndex % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                  }`}>
                    <td className="px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-100">
                      {row.fiscalYear}
                    </td>
                    {row.values.map((cell, cellIndex) => (
                      <td key={cellIndex} className="px-4 py-3 text-sm text-gray-900 border-b border-gray-100">
                        {cell.status ? (
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(cell.status)}`}>
                            {cell.value}
                          </span>
                        ) : (
                          cell.value
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="p-4">
            {isProfitable ? (
              <Alert
                severity="success"
                sx={{
                  backgroundColor: '#a2da9a',
                  color: '#00632a',
                  '& .MuiAlert-icon': {
                    color: '#2e7d32',
                  },
                  '& .MuiAlertTitle-root': {
                    color: '#2e7d32',
                    fontWeight: 'bold',
                  },
                }}
              >
                <AlertTitle>Success</AlertTitle>
                The client is profitable.
              </Alert>
            ) : (
              <Alert severity="error">
                <AlertTitle>Error</AlertTitle>
                The client is not profitable.
              </Alert>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const ProposalSummaryTable = ({ data, expanded, onToggle }) => {
  if (!data || Object.keys(data).length === 0) {
    return (
      <div className="mb-6 overflow-hidden rounded-xl shadow-lg border border-gray-200">
        <SectionHeader
          title="Proposal Summary"
          expanded={expanded}
          onToggle={onToggle}
          icon={<FileText size={20} />}
        />
        {expanded && (
          <div className="bg-white rounded-b-xl p-4">
            <p className="text-gray-500 italic">No proposal data available</p>
          </div>
        )}
      </div>
    );
  }

  const firstProposal = Object.values(data)[0];
  const proposalData = firstProposal.proposal_summary || firstProposal;
  const headers = proposalData.headers || [];
  const values = proposalData.values || [];

  // Auto-calculate missing percentage or deductible values
  const processedValues = [...values];
  const limitIndex = headers.findIndex(h => h === 'Limit of Indemnity (Cover Limit)');
  const deductibleIndex = headers.findIndex(h => h === 'Deductible/Excess Applicable');
  const percentageIndex = headers.findIndex(h => h === 'Excess/ Deductible percentage');

  if (limitIndex !== -1 && deductibleIndex !== -1 && percentageIndex !== -1) {
    const limitValue = parseFloat(String(values[limitIndex] || '0').replace(/,/g, '')) || 0;
    const deductibleValue = parseFloat(String(values[deductibleIndex] || '0').replace(/,/g, '')) || 0;
    const percentageValue = parseFloat(String(values[percentageIndex] || '0').replace(/,/g, '')) || 0;

    // If one field has a value and the other doesn't, calculate the missing one
    if (limitValue > 0) {
      if (deductibleValue > 0 && !percentageValue) {
        // Calculate percentage from deductible
        const calculatedPercentage = (deductibleValue / limitValue) * 100;
        processedValues[percentageIndex] = calculatedPercentage.toFixed(2);
      } else if (percentageValue > 0 && !deductibleValue) {
        // Calculate deductible from percentage
        const calculatedDeductible = (percentageValue * limitValue) / 100;
        processedValues[deductibleIndex] = Math.round(calculatedDeductible).toString();
      }
    }
  }

  // Fields that should have KES and comma formatting
  const monetaryFields = [
    'Estimated Annual Income',
    'Limit of Indemnity (Cover Limit)',
    'Deductible/Excess Applicable'
  ];

  // Format number with commas and KES prefix
  const formatValue = (value, header) => {
    // Handle null, undefined, or empty values
    if (value === null || value === undefined || value === '') {
      return '-';
    }

    // Special formatting for percentage field
    if (header === 'Excess/ Deductible percentage') {
      const numValue = parseFloat(String(value).replace(/,/g, '')) || 0;
      return `${numValue}%`;
    }

    // Only format monetary fields with KES and commas
    if (monetaryFields.includes(header)) {
      // Check if the value is a number or a string that looks like a number
      if (typeof value === 'number' || !isNaN(Number(String(value).replace(/,/g, '')))) {
        const numValue = typeof value === 'number' ? value : Number(String(value).replace(/,/g, ''));
        return `KES ${numValue.toLocaleString('en-US', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        })}`;
      }
    }
    return value;
  };

  // Check for poor proposal quality
  const first11 = processedValues.slice(0, 11);
  const missingCount = first11.filter(
    v => v === null || v === undefined || v === '' || v === '-'
  ).length;
  const hasPoorQuality = missingCount >= 5;

  // Check if there's an uncovered extension warning in proposal summary
  const uncoveredExtensionIndex = headers.findIndex(header => header === 'Uncovered extension');
  const hasUncoveredExtension = uncoveredExtensionIndex !== -1 &&
    processedValues[uncoveredExtensionIndex]?.toString().toLowerCase() === 'yes';

  return (
    <div className="mb-6 overflow-hidden rounded-xl shadow-lg border border-gray-200">
      <SectionHeader
        title="Proposal Summary"
        expanded={expanded}
        onToggle={onToggle}
        icon={<FileText size={20} />}
      />
      {expanded && (
        <div className="bg-white rounded-b-xl">
          <div className="divide-y divide-gray-100">
            {headers.map((header, index) => (
              <DataRow
                key={index}
                label={header}
                value={formatValue(processedValues[index], header)}
                status={header === 'Uncovered extension' && processedValues[index]?.toString().toLowerCase() === 'yes' ? 'warning' : null}
                index={index} // Add this line
              />
            ))}
          </div>
          {hasUncoveredExtension && (
            <div className="p-4">
              <Alert severity="info">
                <AlertTitle>Info</AlertTitle>
                An extension that is not covered has been requested. Consult with the client on this.
              </Alert>
            </div>
          )}
          {hasPoorQuality && (
            <div className="p-4">
              <Alert severity="error">
                <AlertTitle>Error</AlertTitle>
                Poor proposal form quality.
              </Alert>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const InsuranceHistoryTable = ({ data, expanded, onToggle, editMode, setEditMode }) => {
  if (!data || Object.keys(data).length === 0) {
    return (
      <div className="mb-6 overflow-hidden rounded-xl shadow-lg border border-gray-200">
        <SectionHeader
          title="Insurance History"
          expanded={expanded}
          onToggle={onToggle}
          icon={<Eye size={20} />}
        />
        {expanded && (
          <div className="bg-white rounded-b-xl p-4">
            <p className="text-gray-500 italic">No insurance history data available</p>
            {/* Edit Proposal Summary Button */}
            <div className="flex justify-end mt-4 pt-4 border-t border-gray-100">
              <ActionButton
                icon={<Edit size={20} />}
                label={editMode ? 'View Proposal Summary' : 'Edit Proposal Summary'}
                variant="warning"
                onClick={() => setEditMode((prev) => !prev)}
              />
            </div>
          </div>
        )}
      </div>
    );
  }

  const firstProposal = Object.values(data)[0];
  const insuranceData = firstProposal.insurance_history || {};
  const headers = insuranceData.headers || [];
  const values = insuranceData.values || [];

  return (
    <div className="mb-6 overflow-hidden rounded-xl shadow-lg border border-gray-200">
      <SectionHeader
        title="Insurance History"
        expanded={expanded}
        onToggle={onToggle}
        icon={<Eye size={20} />}
      />
      {expanded && (
        <div className="bg-white rounded-b-xl">
          <div className="divide-y divide-gray-100">
            {headers.map((header, index) => (
              <DataRow
                key={index}
                label={header}
                value={values[index] || '-'}
                index={index} // Add this line

              />
            ))}
          </div>
          {/* Edit Proposal Summary Button */}
          <div className="flex justify-end p-4 border-t border-gray-100">
            <ActionButton
              icon={<Edit size={20} />}
              label={editMode ? 'View Proposal Summary' : 'Edit Proposal Summary'}
              variant="warning"
              onClick={() => setEditMode((prev) => !prev)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

const ExtensionsTable = ({ data, expanded, onToggle }) => {
  if (!data || Object.keys(data).length === 0) {
    return (
      <div className="mb-6 overflow-hidden rounded-xl shadow-lg border border-gray-200">
        <SectionHeader
          title="Extensions"
          expanded={expanded}
          onToggle={onToggle}
          icon={<Download size={20} />}
        />
        {expanded && (
          <div className="bg-white rounded-b-xl p-4">
            <p className="text-gray-500 italic">No extensions data available</p>
          </div>
        )}
      </div>
    );
  }

  const firstProposal = Object.values(data)[0];
  const extensionsData = firstProposal.extensions || {};
  const headers = extensionsData.headers || [];
  const values = extensionsData.values || [];

  // Filter out extensions that shouldn't be displayed
  const filteredExtensions = headers.map((header, index) => ({ header, value: values[index] }))
    .filter(({ header }) => {
      // Filter out duplicate "Errors and ommissions" vs "Errors and Omissions"
      if (header === "Errors and ommissions") {
        const hasErrorsAndOmissions = headers.includes("Errors and Omissions");
        if (hasErrorsAndOmissions) {
          return false; // Skip "Errors and ommissions" if "Errors and Omissions" exists
        }
      }

      return true;
    });

  // Check if there's an uncovered extension warning - check in original headers/values, not filtered
  const uncoveredExtensionIndex = headers.findIndex(header => header === 'Uncovered extension');
  const hasUncoveredExtension = uncoveredExtensionIndex !== -1 &&
    values[uncoveredExtensionIndex]?.toString().toLowerCase() === 'yes';

  return (
    <div className="mb-6 overflow-hidden rounded-xl shadow-lg border border-gray-200">
      <SectionHeader
        title="Extensions"
        expanded={expanded}
        onToggle={onToggle}
        icon={<Download size={20} />}
      />
      {expanded && (
        <div className="bg-white rounded-b-xl">
          <div className="divide-y divide-gray-100">
            {filteredExtensions.map(({ header, value }, index) => (
              <DataRow
                key={index}
                label={header}
                value={value || '-'}
                status={header === 'Uncovered extension' && value?.toString().toLowerCase() === 'yes' ? 'warning' : null}
                index={index} // Add this line
              />
            ))}
          </div>
          {hasUncoveredExtension && (
            <div className="p-4">
              <Alert severity="warning">
                <AlertTitle>Warning</AlertTitle>
                An extension that is not covered has been flagged. Consult the client.
              </Alert>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const QuotationTable = ({ data, expanded, onToggle, isLoading, handleGenerateQuote, handleDownloadExcel, handleDownloadWord }) => {
  if (!data || !data.variables) {
    return (
      <div className="mb-6 overflow-hidden rounded-xl shadow-lg border border-gray-200">
        <SectionHeader
          title="Quotation Report"
          expanded={expanded}
          onToggle={onToggle}
          icon={<DollarSign size={20} />}
        />
        {expanded && (
          <div className="bg-white rounded-b-xl p-4">
            <p className="text-gray-500 italic">No quotation data available</p>
            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3 mt-4 pt-4 border-t border-gray-100">
              <ActionButton
                icon={<FileText size={20} />}
                label={isLoading ? "Generating..." : "Generate Quotation"}
                variant="primary"
                onClick={handleGenerateQuote}
                disabled={isLoading}
              />
              <ActionButton
                icon={<Download size={20} />}
                label="Download Excel Quotation"
                variant="success"
                onClick={handleDownloadExcel}
                disabled={true}
              />
              <ActionButton
                icon={<Download size={20} />}
                label="Download Word Quotation"
                variant="success"
                onClick={handleDownloadWord}
                disabled={true}
              />
            </div>
          </div>
        )}
      </div>
    );
  }

  console.log('QuotationTable data:', data);
  console.log('Display values:', data.display_values);

  // Define the order of items and their display names
  const componentLabels = {
    'part_a': 'Annual fees cost',
    'part_b': 'Limit of Indemnity cost',
    'part_c': 'Profession Factor',
    'part_d': 'A + B + C',
    'part_e': 'Loss of Documents extension',
    'part_f': 'Libel & Slander extension',
    'part_g': 'Dishonesty of Employees extension',
    'part_h': 'Incoming/ outgoing partners extension',
    'part_i': 'Errors and ommissions extension',
    'part_j': 'Breach of Authority extension',
    'part_k': 'Basic Premium  ',
    'part_l': 'Excess Deductible discount',
    'levies': 'Levies',
    'stamp_duty': 'Stamp Duty',
    'total_premium': 'Total Premium'
  };

  // Define the order of items
  const orderedKeys = [
    'part_a',
    'part_b',
    'part_c',
   // 'part_d',
    'part_e',
    'part_f',
    'part_g',
    'part_h',
    'part_i',
    'part_j',
    'part_k',
    'part_l',
    'levies',
    'stamp_duty',
    'total_premium'
  ];

  // Format number with commas
  const formatNumber = (num) => {
    if (typeof num === 'number') {
      return num.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });
    }
    return num;
  };

  // Create ordered entries using display_values if available
  const orderedEntries = orderedKeys
    .filter(key => key in data.variables)
    .map(key => {
      let value;
      if (data.display_values && data.display_values[key]) {
        // Use the pre-formatted value from the backend
        value = `KES ${data.display_values[key]}`;
      } else {
        // Fallback to formatting the raw value
        value = `KES ${formatNumber(data.variables[key])}`;
      }
      console.log(`Mapping ${key}:`, { value, display_value: data.display_values?.[key], raw_value: data.variables[key] });
      return [key, value];
    });

  console.log('Ordered entries:', orderedEntries);

  return (
    <div className="mb-6 overflow-hidden rounded-xl shadow-lg border border-gray-200">
      <SectionHeader
        title="Quotation Report"
        expanded={expanded}
        onToggle={onToggle}
        icon={<DollarSign size={20} />}
      />
      {expanded && (
        <div className="bg-white rounded-b-xl">
          <div className="divide-y divide-gray-100">
            {orderedEntries.map(([key, value], index) => {
              const isTotal = key === 'total_premium';
              return (
                <DataRow
                  key={index}
                  label={componentLabels[key]}
                  value={value}
                  status={isTotal ? 'good' : null}
                  index={index} // Add this line

                />
              );
            })}
          </div>
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 p-4 border-t border-gray-100">
            <ActionButton
              icon={<FileText size={20} />}
              label={isLoading ? "Generating..." : "Generate Quotation"}
              variant="primary"
              onClick={handleGenerateQuote}
              disabled={isLoading}
            />
            <ActionButton
              icon={<Download size={20} />}
              label="Download Excel Quotation"
              variant="success"
              onClick={handleDownloadExcel}
              disabled={!data}
            />
            <ActionButton
              icon={<Download size={20} />}
              label="Download Word Quotation"
              variant="success"
              onClick={handleDownloadWord}
              disabled={!data}
            />
          </div>
        </div>
      )}
    </div>
  );
};

// Helper to get all editable fields from the summary
function flattenProposalSummary(proposalSummary) {
  const fields = [];
  ["proposal_summary", "extensions", "insurance_history"].forEach(section => {
    if (proposalSummary[section]) {
      proposalSummary[section].headers.forEach((header, idx) => {
        const value = proposalSummary[section].values[idx];

        // Note: "Uncovered extension" field is now always included regardless of value

        // Filter out duplicate "Errors and ommissions" vs "Errors and Omissions"
        if (header === "Errors and ommissions") {
          // Check if "Errors and Omissions" already exists
          const hasErrorsAndOmissions = proposalSummary[section].headers.includes("Errors and Omissions");
          if (hasErrorsAndOmissions) {
            return; // Skip "Errors and ommissions" if "Errors and Omissions" exists
          }
        }

        fields.push({
          section,
          header,
          value
        });
      });
    }
  });
  return fields;
}

const EditableProposalSummary = ({ proposalSummary, filename, onQuotationResult, setEditMode, onProposalSummaryUpdate }) => {
  // Flatten fields for easier editing
  const [fields, setFields] = useState(flattenProposalSummary(proposalSummary));
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [validation, setValidation] = useState({});
  const [showRequiredError, setShowRequiredError] = useState(false);

  // Get the first 11 headers from proposalSummary for required fields
  // const requiredHeaders = proposalSummary?.headers?.slice(0, 11) || [];

  // Field type mapping for validation and input type
  const fieldTypeMap = {
    // Proposal Summary
    'Name of Cedant': 'text',
    'Name of Broker': 'text',
    'Period of Cover ': 'text',
    'Number of Business Partners': 'number',
    'Number of Qualified staff': 'number',
    'Number of Unqualified Staff Employed': 'number',
    //'Other staff': 'number',
    'Estimated Annual Income': 'number',
    'Limit of Indemnity (Cover Limit)': 'number',
    'Deductible/Excess Applicable': 'number',
    'Excess/ Deductible percentage': 'number',
    'Occupation of the Insured': 'text',
    'Retroactive period': 'retroactive_select',
    // Extensions (all boolean)
    'Defamation Coverage': 'boolean',
    'Loss of Documents': 'boolean',
    'Errors and ommissions': 'boolean',
    'Errors and Omissions': 'boolean',
    'Incoming/Outgoing partners': 'boolean',
    'Dishonesty of employees': 'boolean',
    'Breach of Authority': 'boolean',
    'Uncovered extension': 'boolean',
    // Insurance History (all boolean except Insurance Company)
    'Insurance Company': 'text',
    'Declined previous insurance proposal': 'boolean',
    'Terminated/ refused insurance renewal': 'boolean',
    'Claims setted/ outstanding': 'boolean',
    'Claim circumstance awareness': 'boolean',
  };

  // Helper to determine field type
  const getFieldType = (header, section) => {
    const normalizedHeader = header.trim().toLowerCase();
    if (normalizedHeader === 'insurance company' || normalizedHeader === 'insurance company') return 'text';
    if (fieldTypeMap[header]) return fieldTypeMap[header];
    if (section === 'extensions' || (section === 'insurance_history' && normalizedHeader !== 'insurance company' && normalizedHeader !== 'insurance company')) return 'boolean';
    return 'text';
  };

  // Helper to determine if a field is required (Name of Broker is no longer required)
  const requiredProposalFields = [
    'Name of Cedant',
    // 'Name of Broker', // Removed as per requirement
    'Period of Cover',
    'Number of Business Partners',
    'Number of Qualified staff',
    'Number of Unqualified staff',
    //'Other staff',
    'Estimated Annual Income',
    'Limit of Indemnity (Cover Limit)',
    'Occupation of the Insured',
    'Deductible/Excess Applicable',
    // Note: 'Excess/ Deductible percentage' is auto-calculated, so not required for manual input
  ];
  const isRequiredField = (header, section) => {
    return section === 'proposal_summary' && requiredProposalFields.includes(header);
  };

  // Group fields by section for section headers
  const groupedFields = fields.reduce((acc, field) => {
    if (!acc[field.section]) acc[field.section] = [];
    acc[field.section].push(field);
    return acc;
  }, {});

  // Validation logic
  const validateField = (field) => {
    const type = getFieldType(field.header, field.section);
    const value = field.value;
    // Required field check
    if (isRequiredField(field.header, field.section) && (value === '' || value === null || value === undefined)) {
      return 'Required';
    }
    // Special validation for Insurance Company in insurance_history
    if (field.section === 'insurance_history' && field.header === 'Insurance Company') {
      if (value === '' || value === null || value === undefined) {
        return '';
      }
      if (typeof value !== 'string' || !isNaN(Number(value))) {
        return 'Must be text';
      }
      return '';
    }
    if (value === '' || value === null || value === undefined) {
      return '';
    }
    if (type === 'number') {
      if (isNaN(Number(value))) {
        return 'Must be a number';
      }
      if (Number(value) < 0) {
        return 'Must be non-negative';
      }
      // Special validation for percentage field
      if (field.header === 'Excess/ Deductible percentage' && Number(value) > 100) {
        return 'Percentage cannot exceed 100%';
      }
    }
    if (type === 'boolean') {
      if (value !== 'Yes' && value !== 'No') {
        return 'Select Yes or No';
      }
    }
    return '';
  };

  // Validate all fields
  const validateAll = () => {
    const newValidation = {};
    let hasError = false;
    fields.forEach((field) => {
      const errorMsg = validateField(field);
      if (errorMsg) hasError = true;
      newValidation[`${field.section}__${field.header}`] = errorMsg;
    });
    setValidation(newValidation);
    return !hasError;
  };

  // Handle input change with automatic calculation for deductible/percentage fields
  const handleChange = (section, header, newValue) => {
    setFields(prev => {
      const updatedFields = prev.map((field) =>
        field.section === section && field.header === header
          ? { ...field, value: newValue }
          : field
      );

      // Auto-calculate related fields for deductible/excess and percentage
      if (section === 'proposal_summary') {
        const limitField = updatedFields.find(f => f.header === 'Limit of Indemnity (Cover Limit)');
        const deductibleField = updatedFields.find(f => f.header === 'Deductible/Excess Applicable');
        const percentageField = updatedFields.find(f => f.header === 'Excess/ Deductible percentage');

        const limitValue = parseFloat(String(limitField?.value || '0').replace(/,/g, '')) || 0;

        if (header === 'Deductible/Excess Applicable' && limitValue > 0) {
          // Calculate percentage from deductible
          const deductibleValue = parseFloat(String(newValue || '0').replace(/,/g, '')) || 0;
          const calculatedPercentage = (deductibleValue / limitValue) * 100;

          if (percentageField) {
            const percentageIndex = updatedFields.findIndex(f => f.header === 'Excess/ Deductible percentage');
            if (percentageIndex !== -1) {
              updatedFields[percentageIndex] = {
                ...updatedFields[percentageIndex],
                value: calculatedPercentage.toFixed(2)
              };
            }
          }
        } else if (header === 'Excess/ Deductible percentage' && limitValue > 0) {
          // Calculate deductible from percentage
          const percentageValue = parseFloat(String(newValue || '0').replace(/,/g, '')) || 0;
          const calculatedDeductible = (percentageValue * limitValue) / 100;

          if (deductibleField) {
            const deductibleIndex = updatedFields.findIndex(f => f.header === 'Deductible/Excess Applicable');
            if (deductibleIndex !== -1) {
              updatedFields[deductibleIndex] = {
                ...updatedFields[deductibleIndex],
                value: Math.round(calculatedDeductible).toString()
              };
            }
          }
        } else if (header === 'Limit of Indemnity (Cover Limit)') {
          // Recalculate based on existing deductible value when limit changes
          const deductibleValue = parseFloat(String(deductibleField?.value || '0').replace(/,/g, '')) || 0;
          const newLimitValue = parseFloat(String(newValue || '0').replace(/,/g, '')) || 0;

          if (deductibleValue > 0 && newLimitValue > 0 && percentageField) {
            const calculatedPercentage = (deductibleValue / newLimitValue) * 100;
            const percentageIndex = updatedFields.findIndex(f => f.header === 'Excess/ Deductible percentage');
            if (percentageIndex !== -1) {
              updatedFields[percentageIndex] = {
                ...updatedFields[percentageIndex],
                value: calculatedPercentage.toFixed(2)
              };
            }
          }
        }
      }

      return updatedFields;
    });

    // Validate on change and clear validation for auto-calculated fields
    setValidation(prev => {
      const newValidation = {
        ...prev,
        [`${section}__${header}`]: validateField({ section, header, value: newValue })
      };

      // Clear validation errors for auto-calculated fields
      if (section === 'proposal_summary') {
        if (header === 'Deductible/Excess Applicable' || header === 'Limit of Indemnity (Cover Limit)') {
          newValidation[`${section}__Excess/ Deductible percentage`] = '';
        } else if (header === 'Excess/ Deductible percentage') {
          newValidation[`${section}__Deductible/Excess Applicable`] = '';
        }
      }

      return newValidation;
    });
    setShowRequiredError(false);
  };

  // Update backend and generate quote
  const handleGenerateQuotation = async () => {
    if (!validateAll()) {
      setShowRequiredError(true);
      return;
    }
    setShowRequiredError(false);
    setLoading(true);
    setSuccess(false);
    setError(null);
    try {
      // Update each field in backend
      for (const field of fields) {
        await editProposalField({
          filename,
          section: field.section,
          field: field.header,
          value: field.value
        });
      }
      // Generate new quotation
      const res = await generateQuote();
      onQuotationResult(res.data);
      setSuccess(true);
      // Build updated proposalSummary object
      const updatedProposal = { ...proposalSummary };
      ["proposal_summary", "extensions", "insurance_history"].forEach(section => {
        if (updatedProposal[section]) {
          updatedProposal[section] = {
            ...updatedProposal[section],
            values: updatedProposal[section].headers.map((header, idx) => {
              const field = fields.find(f => f.section === section && f.header === header);
              return field ? field.value : updatedProposal[section].values[idx];
            })
          };
        }
      });
      if (onProposalSummaryUpdate) {
        onProposalSummaryUpdate(updatedProposal);
      }
      if (setEditMode) {
        setEditMode(false);
      }
    } catch (err) {
      setError(
        err.response?.data?.error ||
        err.message ||
        "An error occurred while updating or generating the quotation."
      );
    }
    setLoading(false);
  };

  // Section display names
  const sectionNames = {
    proposal_summary: 'Proposal Summary',
    extensions: 'Extensions',
    insurance_history: 'Insurance History',
  };

  return (
    <div style={{ padding: 24, marginBottom: 24, maxWidth: '100%', overflowX: 'auto' }}>
      <h2 className="text-lg font-semibold mb-4 text-gray-800">Edit Proposal Form</h2>
      <TableContainer sx={{ 
        maxHeight: 'unset', 
        height: 'auto', 
        boxShadow: 'none', 
        border: 'none', 
        background: 'none', 
        width: '80%', 
        margin: '0 auto',
        '& .MuiTable-root': {
          borderRadius: '12px',
          overflow: 'hidden'
        }
      }}>
        <Table stickyHeader aria-label="editable proposal summary table">
          <TableHead>
            <TableRow>
              <StyledTableCell>Field</StyledTableCell>
              <StyledTableCell>Value</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Object.entries(groupedFields).map(([section, sectionFields]) => {
              // Check if uncovered extension is "Yes" for this section
              const hasUncoveredExtension = section === 'extensions' &&
                proposalSummary?.extensions?.headers?.includes('Uncovered extension') &&
                proposalSummary?.extensions?.values?.[proposalSummary.extensions.headers.indexOf('Uncovered extension')]?.toString().toLowerCase() === 'yes';

              return [
                // Add info alert before Extensions heading if uncovered extension is Yes
                ...(hasUncoveredExtension ? [
                  <StyledTableRow key={`${section}-alert`}>
                    <StyledTableCell colSpan={2} sx={{ padding: 0, border: 'none' }}>
                      <Alert severity="info" sx={{ margin: 1 }}>
                        <AlertTitle>Info</AlertTitle>
                        An extension that is not covered has been requested. Consult with the client on this.
                      </Alert>
                    </StyledTableCell>
                  </StyledTableRow>
                ] : []),
                <StyledTableRow key={section}>
                  <StyledTableCell colSpan={2} sx={{ backgroundColor: '#e3eafc', fontWeight: 'bold' }}>
                    {sectionNames[section] || section}
                  </StyledTableCell>
                </StyledTableRow>,
                ...sectionFields.map((field, idx) => {
                  const type = getFieldType(field.header, field.section);
                  const key = `${field.section}__${field.header}`;
                  // --- Fix: Always render Insurance Company as text input ---
                  const normalizedHeader = field.header.trim().toLowerCase();
                  const isInsuranceCompany = normalizedHeader === 'insurance company' || normalizedHeader === 'insurance company';
                  return (
                    <StyledTableRow key={key}
                      sx={{ backgroundColor: idx % 2 === 0 ? 'inherit' : '#f5f7fa' }}
                    >
                      <StyledTableCell>
                        {field.header}
                      </StyledTableCell>
                      <StyledTableCell>
                        {type === 'boolean' && !isInsuranceCompany ? (
                          <TextField
                            select
                            variant="outlined"
                            size="small"
                            value={field.value ?? ''}
                            onChange={e => handleChange(field.section, field.header, e.target.value)}
                            fullWidth
                            error={!!validation[key]}
                            helperText={validation[key]}
                          >
                            <MenuItem value="Yes">Yes</MenuItem>
                            <MenuItem value="No">No</MenuItem>
                          </TextField>
                        ) : type === 'retroactive_select' ? (
                          <TextField
                            select
                            variant="outlined"
                            size="small"
                            value={field.value ?? ''}
                            onChange={e => handleChange(field.section, field.header, e.target.value)}
                            fullWidth
                            error={!!validation[key]}
                            helperText={validation[key]}
                          >
                            <MenuItem value="">None</MenuItem>
                            <MenuItem value="12">12 months</MenuItem>
                            <MenuItem value="24">24 months</MenuItem>
                          </TextField>
                        ) : (
                          <TextField
                            variant="outlined"
                            size="small"
                            type={type === 'number' ? 'number' : 'text'}
                            value={field.value ?? ''}
                            onChange={e => handleChange(field.section, field.header, e.target.value)}
                            fullWidth
                            error={!!validation[key]}
                            helperText={validation[key]}
                            slotProps={{
                              input: {
                                inputProps: type === 'number' ? { min: 0 } : {},
                                style: { background: '#fff' }
                              }
                            }}
                          />
                        )}
                      </StyledTableCell>
                    </StyledTableRow>
                  );
                })
              ];
            })}
          </TableBody>
        </Table>
      </TableContainer>
      <div className="flex items-center mt-6 gap-4">
        <button
          onClick={handleGenerateQuotation}
          disabled={loading}
          className={`px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-green-400 disabled:cursor-not-allowed flex items-center`}
        >
          {loading ? (
            <CircularProgress size={22} sx={{ color: 'white', mr: 1 }} />
          ) : (
            <CheckCircleIcon sx={{ mr: 1 }} />
          )}
          Save & Generate Quotation
        </button>
        {showRequiredError && !loading && (
          <span className="text-red-700 flex items-center"><ErrorIcon sx={{ mr: 0.5 }} /> fill all required fields</span>
        )}
        {success && (
          <span className="text-green-700 flex items-center"><CheckCircleIcon sx={{ mr: 0.5 }} /> Saved!</span>
        )}
        {error && (
          <span className="text-red-700 flex items-center"><ErrorIcon sx={{ mr: 0.5 }} /> {error}</span>
        )}
      </div>
    </div>
  );
};

const Summary = () => {
  const { financialSummary, proposalSummary, setProposalSummary } = useSummary();
  const [quotation, setQuotation] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);

  // State for collapsible sections
  const [expandedSections, setExpandedSections] = useState({
    financial: true,
    proposal: true,
    extensions: true,
    insurance: true,
    quotation: true
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Get the filename and proposal for editing
  const filename = proposalSummary && Object.keys(proposalSummary).length > 0 ? Object.keys(proposalSummary)[0] : null;
  const proposalForEdit = filename ? proposalSummary[filename] : null;

  // Function to check if any of the last 4 insurance history values are "Yes"
  // const hasInsuranceWarning = () => {
  //   if (!proposalSummary || Object.keys(proposalSummary).length === 0) return false;
  //
  //   const firstProposal = Object.values(proposalSummary)[0];
  //   const insuranceData = firstProposal.insurance_history || {};
  //   const values = insuranceData.values || [];
  //
  //   // Get the last 4 values and check if any are "Yes"
  //   const lastFourValues = values.slice(-4);
  //   return lastFourValues.some(value => value?.toString().toLowerCase() === 'yes');
  // };

  const isEmpty = (!proposalSummary || Object.keys(proposalSummary).length === 0) && (!financialSummary || !financialSummary.financial_summary || financialSummary.financial_summary.length === 0);

  const handleGenerateQuote = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await generateQuote();
      setQuotation(data);
      console.log('Generated quotation:', data);
    } catch (err) {
      setError(err.message);
      console.error('Error generating quotation:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadExcel = async () => {
    try {
      const response = await downloadQuote();

      // Check if the response is empty
      const contentLength = response.headers.get('Content-Length');
      if (contentLength === '0') {
        throw new Error('Empty response received from server');
      }

      const blob = await response.blob();
      if (blob.size === 0) {
        throw new Error('Empty file received from server');
      }

      // Get filename from Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'quotation.xlsx'; // Default filename
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename\*=UTF-8''([^;]+)/);
        if (filenameMatch) {
          filename = decodeURIComponent(filenameMatch[1]);
        }
      }

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error downloading excel:', err);
      setError(err.message);
    }
  };

  const handleDownloadWord = async () => {
    try {
      const response = await downloadWordQuote();

      // Check if the response is empty
      const contentLength = response.headers.get('Content-Length');
      if (contentLength === '0') {
        throw new Error('Empty response received from server');
      }

      const blob = await response.blob();
      if (blob.size === 0) {
        throw new Error('Empty file received from server');
      }

      // Get filename from Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'quotation.docx'; // Default filename
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename\*=UTF-8''([^;]+)/);
        if (filenameMatch) {
          filename = decodeURIComponent(filenameMatch[1]);
        }
      }

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error downloading word:', err);
      setError(err.message);
    }
  };

  return (
    <div className="w-full flex justify-center">
      <div className="w-4/5"> {/* This div constrains width to 80% */}
        {isEmpty ? (
          <div className="p-8 text-center">
            <div className="text-gray-500 italic">Please review the extracted values carefully, as AI extractions may contain inaccuracies. Make any necessary adjustments to ensure data accuracy.</div>
          </div>
        ) : (
          <>
            {/* Financial Summary Section */}
            <FinancialSummaryTable
              data={financialSummary}
              expanded={expandedSections.financial}
              onToggle={() => toggleSection('financial')}
            />

            {editMode && proposalForEdit && filename ? (
              <div className="mb-6 overflow-hidden rounded-xl shadow-lg border border-gray-200">
                <SectionHeader
                  title="Edit Proposal Summary"
                  expanded={true}
                  onToggle={() => {}}
                  icon={<Edit size={20} />}
                />
                <div className="bg-white rounded-b-xl">
                  <EditableProposalSummary
                  proposalSummary={proposalForEdit}
                  filename={filename}
                  onQuotationResult={setQuotation}
                  setEditMode={setEditMode}
                  onProposalSummaryUpdate={updatedProposal => {
                    // Update the proposalSummary in context
                    setProposalSummary(prev => ({
                      ...prev,
                      [filename]: updatedProposal
                    }));
                  }}
                />
                </div>
              </div>
            ) : (
              <>
                {/* Proposal Summary Section */}
                <ProposalSummaryTable
                  data={proposalSummary}
                  expanded={expandedSections.proposal}
                  onToggle={() => toggleSection('proposal')}
                />



                {/* Extensions Section */}
                <ExtensionsTable
                  data={proposalSummary}
                  expanded={expandedSections.extensions}
                  onToggle={() => toggleSection('extensions')}
                />

                {/* Insurance History Section */}
                <InsuranceHistoryTable
                  data={proposalSummary}
                  expanded={expandedSections.insurance}
                  onToggle={() => toggleSection('insurance')}
                  editMode={editMode}
                  setEditMode={setEditMode}
                />
              </>
            )}

            {/* Insurance History Alerts */}
            {(() => {
              if (!proposalSummary || Object.keys(proposalSummary).length === 0) return null;
              const firstProposal = Object.values(proposalSummary)[0];
              const insuranceData = firstProposal.insurance_history || {};
              const headers = insuranceData.headers || [];
              const values = insuranceData.values || [];
              // Normalize function
              const normalize = str => str?.toString().trim().toLowerCase();
              // Map normalized header to value
              const normalizedFieldMap = {};
              headers.forEach((header, idx) => {
                normalizedFieldMap[normalize(header)] = values[idx];
              });
              const alerts = [];
              if (
                normalizedFieldMap[normalize('Declined previous insurance proposal')]?.toString().toLowerCase() === 'yes'
              ) {
                alerts.push(
                  <Alert severity="info" key="declined">
                    <AlertTitle>Info</AlertTitle>
                    The cedant has previously had a declined insurance proposal.
                    Request for more information.
                  </Alert>
                );
              }
              if (
                normalizedFieldMap[normalize('Terminated/ refused insurance renewal')]?.toString().toLowerCase() === 'yes'
              ) {
                alerts.push(
                  <Alert severity="info" key="terminated">
                    <AlertTitle>Info</AlertTitle>
                    The cedant has previously had insurance renewal terminated or refused.
                    Request for more information.
                  </Alert>
                );
              }
              if (normalizedFieldMap[normalize('Claims setted/ outstanding')]?.toString().toLowerCase() === 'yes') {
                alerts.push(
                  <Alert severity="info" key="claims">
                    <AlertTitle>Info</AlertTitle>
                    The cedant has previously had claims settled or outstanding.
                    Request for more information.
                  </Alert>
                );
              }
              if (normalizedFieldMap[normalize('Claim circumstance awareness')]?.toString().toLowerCase() === 'yes') {
                alerts.push(
                  <Alert severity="info" key="circumstance">
                    <AlertTitle>Info</AlertTitle>
                    The cedant has claim circumstance awareness.
                    Request for more information.
                  </Alert>
                );
              }
              return alerts.length > 0 ? (
                <div className="bg-white rounded-xl shadow-lg mb-6 p-4 space-y-4">
                  {alerts}
                </div>
              ) : null;
            })()}



            {/* Quotation Section */}
            <QuotationTable
              data={quotation}
              expanded={expandedSections.quotation}
              onToggle={() => toggleSection('quotation')}
              isLoading={isLoading}
              handleGenerateQuote={handleGenerateQuote}
              handleDownloadExcel={handleDownloadExcel}
              handleDownloadWord={handleDownloadWord}
            />

            {/* Error Display */}
            {error && (
              <div className="bg-white rounded-xl shadow-lg mb-6 p-4">
                <Alert severity="error">
                  <AlertTitle>Error</AlertTitle>
                  {error}
                </Alert>
              </div>
            )}


          </>
        )}
      </div>
    </div>
  );
};

export default Summary;
